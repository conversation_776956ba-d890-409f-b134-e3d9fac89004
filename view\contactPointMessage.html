<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>我的留言</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none
    }

    .body_box {
      background: #f5f6fa;
      height: 100vh;
    }

    /* 卡片容器 */
    .card-container {
      gap: 10px;
      padding: 12px 16px 16px 16px;
    }

    .my-message-card {
      width: 195px;
      height: 100px;
      overflow: hidden;
      position: relative;
    }

    .public-message-card {
      width: 150px;
      height: 100px;
      overflow: hidden;
      position: relative;
    }

    .card-bg-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .card-content {
      position: relative;
      z-index: 2;
      margin: 8px 10px;
    }

    .card-title {
      font-weight: 500;
      font-size: 17px;
      color: #FFFFFF;
    }

    .my-count-box {
      margin: 18px 0 0 65px;
    }

    .public-count-box {
      margin-top: 15px;
    }

    .triangle {
      width: 0;
      height: 0;
      border-top: 5px solid transparent;
      border-bottom: 5px solid transparent;
      border-left: 5px solid #fff;
      margin-right: 4px;
    }

    .message-count {
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;
    }

    .replied-count {
      font-size: 14px;
      flex: 1;
      color: #fff;
    }

    .view-more {
      font-size: 14px;
      color: #fff;
    }

    .message-section {
      background: #fff;
      margin-bottom: 14px;
      padding: 15px;
    }

    .more-btn {
      color: #999999;
      font-size: 14px;
    }

    .more-img {
      width: 20px;
      height: 20px;
    }

    .message-list {
      margin-top: 8px;
    }

    .message-item {
      align-items: flex-end;
      padding: 12px 0;
    }

    .message-icon {
      width: 20px;
      height: 20px;
    }

    .message-title {
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      margin-left: 4px;
    }

    .message-info {
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      margin-top: 10px;
    }

    .sender {
      margin-right: 10px;
    }

    .message-right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: space-between;
      position: relative;
    }

    .status-replied {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 2px;
      background: rgba(80, 198, 20, 0.1);
      color: rgb(80, 198, 20);
    }

    .status-unreplied {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 2px;
      color: #F6631C;
      background: rgba(246, 99, 28, 0.08);
    }

    /* 留言 */
    .message-button {
      position: fixed;
      bottom: 100px;
      right: 16px;
      width: 50px;
      height: 50px;
      background: #104B8B;
      box-shadow: 0px 4px 12px 0px rgba(24, 64, 118, 0.15);
      border-radius: 50%;
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
    }

    .nodata {
      margin: 30px 0;
      text-align: center;
      color: #ccc;
      width: 100%;
      font-size: 14px;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 顶部卡片区域 -->
    <div class="card-container flex_box flex_justify_between">
      <div class="my-message-card" @click="viewMessages('my')">
        <img class="card-bg-image" src="../img/icon_my_message_bg.png" mode="aspectFill" />
        <div class="card-content">
          <div class="card-title">我的留言</div>
          <div class="my-count-box">
            <div class="flex_box flex_align_center">
              <span class="triangle"></span>
              <span class="triangle"></span>
              <span class="message-count">{{myMessageCount}}封</span>
            </div>
            <div class="flex_box flex_align_center" style="margin-top: 4px;">
              <span class="replied-count">已回复{{myRepliedCount}}封</span>
              <span class="view-more">查看></span>
            </div>
          </div>
        </div>
      </div>
      <div class="public-message-card" @click="viewMessages('public')">
        <img class="card-bg-image" src="../img/icon_public_message_bg.png" mode="aspectFill" />
        <div class="card-content">
          <div class="card-title">公开留言</div>
          <div class="public-count-box">
            <div class="flex_box flex_align_center">
              <span class="triangle"></span>
              <span class="triangle"></span>
              <span class="message-count">{{publicMessageCount}}封</span>
            </div>
            <div class="flex_box flex_align_center" style="margin-top: 4px;">
              <span class="replied-count">已回复{{publicRepliedCount}}封</span>
              <span class="view-more">查看></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 我的留言 -->
    <div class="message-section">
      <div class="flex_box flex_align_center flex_justify_between">
        <div class="section-line"></div>
        <div class="section-title">我的留言</div>
        <div class="more-btn flex_box flex_align_center" @click="viewMessages('my')">
          <span class="more-text">全部</span>
          <image src="../img/arrow_right.png" mode="" class="more-img" />
        </div>
      </div>
      <div class="message-list">
        <template v-if="myMessages.length>0">
          <div class="message-item flex_box" v-for="item in myMessages" :key="item.id"
            @click="openMessagesDetails(item)">
            <div class="flex_1">
              <div class="flex_box flex_align_center">
                <img class="message-icon" src="../img/icon_read.png"></image>
                <div class="message-title one_text">{{item.title}}</div>
              </div>
              <div class="message-info flex_box flex_align_center">
                <span class="sender">写信人：{{item.senderUserName}}</span>
                <span class="time">{{formatTime(item.receiveTime)}}</span>
              </div>
            </div>
            <div class="message-right">
              <span :class="item.hasAnswer ? 'status-replied' :'status-unreplied'">{{item.hasAnswer ? '已回复' :
                '未回复'}}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="nodata">暂无数据</div>
        </template>
      </div>
    </div>
    <!-- 公开留言列表 -->
    <div class="message-section">
      <div class="flex_box flex_align_center flex_justify_between">
        <div class="section-line"></div>
        <div class="section-title">公开留言</div>
        <div class="more-btn flex_box flex_align_center" @click="viewMessages('public')">
          <span class="more-text">全部</span>
          <image src="../img/arrow_right.png" mode="" class="more-img" />
        </div>
      </div>
      <div class="message-list">
        <template v-if="publicMessages&&publicMessages.length>0">
          <div class="message-item flex_box" v-for="item in publicMessages" :key="item.id"
            @click="openMessagesDetails(item)">
            <div class="flex_1">
              <div class="flex_box flex_align_center">
                <img class="message-icon" src="../img/icon_read.png"></image>
                <div class="message-title one_text">{{item.title}}</div>
              </div>
              <div class="message-info flex_box flex_align_center">
                <span class="sender">写信人：{{item.senderUserName}}</span>
                <span class="time">{{formatTime(item.receiveTime)}}</span>
              </div>
            </div>
            <div class="message-right">
              <span :class="item.hasAnswer ? 'status-replied' :'status-unreplied'">{{item.hasAnswer ? '已回复' :
                '未回复'}}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="nodata">暂无数据</div>
        </template>
      </div>
    </div>
    <!-- 留言按钮 -->
    <div class="message-button flex_box flex_align_center flex_justify_center" @click="onMessageButtonClick">留言</div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var areaKey = '00871c7d56551fb20bde67a129649e7faf95c05d9203d47ed6de529df5722303b4' // 私钥
    var publicMessageCountKey = '045b4e75032df41d536233631f426ef247ac738049ab291d1612df02801cb1f37cf9ef7949e5f00cecdb55411b3822deef5b03b8ab48fa8b45ebc546d1d88c32f5' // 留言统计公钥
    var publicMessageListKey = '045b4e75032df41d536233631f426ef247ac738049ab291d1612df02801cb1f37cf9ef7949e5f00cecdb55411b3822deef5b03b8ab48fa8b45ebc546d1d88c32f5' // 留言列表公钥

    var id = ''
    var app = new Vue({
      el: "#app",
      data: {
        myMessageCount: 7,
        myRepliedCount: 0,
        publicMessageCount: 9,
        publicRepliedCount: 0,
        myMessages: [{ "id": "1983083194739613698", "serialNumber": "20251028000002", "title": "测试留言", "content": "123456", "letterMobile": null, "receiveTime": 1761638852187, "receiveLetterType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasAnswer": 0, "receiverId": "0", "senderId": "o9RxP12L0elS0Vn-6LSiWnXIRQk8", "senderUserName": "LoYal", "senderType": null, "isOpenAdmin": 1, "isOpenSender": 1, "checkStatus": 1, "terminalName": "PUBLIC", "businessId": null, "createDate": 1761638852188, "businessCode": "law_contact_point", "extYellow": { "value": null, "label": null, "name": null, "dictCode": null }, "extRed": "1", "extBlue": "1981253626277953538", "extGreen": null, "senderHeadImg": null, "senderPhoto": null, "receiverName": null, "receiverTel": null, "email": null, "userRoles": ["普通用户"], "answers": null, "extProperties": { "extBlueName": "测试立法联系点" } }, { "id": "1981519606103179265", "serialNumber": "20251024000002", "title": "111", "content": "测试", "letterMobile": "***********", "receiveTime": 1761266063606, "receiveLetterType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasAnswer": 0, "receiverId": "0", "senderId": "1", "senderUserName": "Admin", "senderType": null, "isOpenAdmin": 1, "isOpenSender": 1, "checkStatus": 1, "terminalName": "APP", "businessId": null, "createDate": 1761266063606, "businessCode": "law_contact_point", "extYellow": { "value": null, "label": null, "name": null, "dictCode": null }, "extRed": "1", "extBlue": "1981253626277953538", "extGreen": null, "senderHeadImg": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "senderPhoto": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "receiverName": null, "receiverTel": null, "email": "", "userRoles": ["人大代表"], "answers": null, "extProperties": { "extBlueName": "测试立法联系点" } }, { "id": "1981518950734790658", "serialNumber": "20251024000001", "title": "测试", "content": "1", "letterMobile": "***********", "receiveTime": 1761265907352, "receiveLetterType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasAnswer": 1, "receiverId": "0", "senderId": "1", "senderUserName": "Admin", "senderType": null, "isOpenAdmin": 1, "isOpenSender": 1, "checkStatus": 1, "terminalName": "APP", "businessId": null, "createDate": 1761265907355, "businessCode": "law_contact_point", "extYellow": { "value": null, "label": null, "name": null, "dictCode": null }, "extRed": "1", "extBlue": "1981253626277953538", "extGreen": null, "senderHeadImg": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "senderPhoto": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "receiverName": null, "receiverTel": null, "email": "", "userRoles": ["人大代表"], "answers": null, "extProperties": { "extBlueName": "测试立法联系点" } }],
        publicMessages: [{ "id": "1983083194739613698", "serialNumber": "20251028000002", "title": "测试留言", "content": "123456", "letterMobile": null, "receiveTime": 1761638852187, "receiveLetterType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasAnswer": 0, "receiverId": "0", "senderId": "o9RxP12L0elS0Vn-6LSiWnXIRQk8", "senderUserName": "LoYal", "senderType": null, "isOpenAdmin": 1, "isOpenSender": 1, "checkStatus": 1, "terminalName": "PUBLIC", "businessId": null, "createDate": 1761638852188, "businessCode": "law_contact_point", "extYellow": { "value": null, "label": null, "name": null, "dictCode": null }, "extRed": "1", "extBlue": "1981253626277953538", "extGreen": null, "senderHeadImg": null, "senderPhoto": null, "receiverName": null, "receiverTel": null, "email": null, "userRoles": ["普通用户"], "answers": null, "extProperties": { "extBlueName": "测试立法联系点" } }, { "id": "1981519606103179265", "serialNumber": "20251024000002", "title": "111", "content": "测试", "letterMobile": "***********", "receiveTime": 1761266063606, "receiveLetterType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasAnswer": 0, "receiverId": "0", "senderId": "1", "senderUserName": "Admin", "senderType": null, "isOpenAdmin": 1, "isOpenSender": 1, "checkStatus": 1, "terminalName": "APP", "businessId": null, "createDate": 1761266063606, "businessCode": "law_contact_point", "extYellow": { "value": null, "label": null, "name": null, "dictCode": null }, "extRed": "1", "extBlue": "1981253626277953538", "extGreen": null, "senderHeadImg": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "senderPhoto": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "receiverName": null, "receiverTel": null, "email": "", "userRoles": ["人大代表"], "answers": null, "extProperties": { "extBlueName": "测试立法联系点" } }, { "id": "1981518950734790658", "serialNumber": "20251024000001", "title": "测试", "content": "1", "letterMobile": "***********", "receiveTime": 1761265907352, "receiveLetterType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasAnswer": 1, "receiverId": "0", "senderId": "1", "senderUserName": "Admin", "senderType": null, "isOpenAdmin": 1, "isOpenSender": 1, "checkStatus": 1, "terminalName": "APP", "businessId": null, "createDate": 1761265907355, "businessCode": "law_contact_point", "extYellow": { "value": null, "label": null, "name": null, "dictCode": null }, "extRed": "1", "extBlue": "1981253626277953538", "extGreen": null, "senderHeadImg": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "senderPhoto": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "receiverName": null, "receiverTel": null, "email": "", "userRoles": ["人大代表"], "answers": null, "extProperties": { "extBlueName": "测试立法联系点" } }]
      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        this.getMessagesCount()
      },
      methods: {
        // 获取留言统计
        getMessagesCount () {
          var that = this
        },
        // 获取留言列表
        getMessagesList () {
          var that = this
        },
        // 查看留言
        viewMessages (type) {
          console.log('查看留言', type)
          window.location.href = './messageList.html?type=' + type
        },
        // 打开留言详情
        openMessagesDetails (item) {
          console.log('打开留言详情', item)
          window.location.href = './messageDetails.html?id=' + item.id
        },
        // 跳转到留言页面
        onMessageButtonClick () {
          window.location.href = 'contactPointMessageAdd.html?id' + id
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>